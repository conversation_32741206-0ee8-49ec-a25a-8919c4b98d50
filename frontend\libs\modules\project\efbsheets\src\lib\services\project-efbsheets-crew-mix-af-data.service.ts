/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable, InjectionToken } from '@angular/core';
import { ServiceRole, IDataServiceOptions, IDataServiceChildRoleOptions, IDataServiceEndPointOptions, DataServiceFlatLeaf } from '@libs/platform/data-access';
import { BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN, childType } from '@libs/basics/interfaces';
import { ProjectEfbsheetsDataService } from './project-efbsheets-data.service';
import { PlatformLazyInjectorService } from '@libs/platform/common';
import { IPrjEfbsheetsCrewMixEntity, IPrjEfbsheetsMixAfEntity } from '@libs/project/interfaces';
import { IPrjCrewMixComplete } from '../model/prj-crew-mix-complete.interface';
import { ProjectMainDataService } from '@libs/project/shared';

export const PROJECT_EFBSHEETS_CREW_MIX_AF_DATA_TOKEN = new InjectionToken<ProjectEfbsheetsCrewMixAfDataService>('projectEfbsheetsProjectCrewMixAfDataToken');
@Injectable({
	providedIn: 'root',
})
export class ProjectEfbsheetsCrewMixAfDataService extends DataServiceFlatLeaf<IPrjEfbsheetsMixAfEntity, IPrjEfbsheetsCrewMixEntity, IPrjCrewMixComplete> {
	private readonly lazyInjector = inject(PlatformLazyInjectorService);
	private readonly projectEfbsheetsProjectDataService = inject(ProjectEfbsheetsDataService);
	private readonly projectEfbsheetstDataService = inject(ProjectEfbsheetsDataService);
	private readonly projectMainDataService = inject(ProjectMainDataService);
	public constructor(parentService: ProjectEfbsheetsDataService) {
		const options: IDataServiceOptions<IPrjEfbsheetsMixAfEntity> = {
			apiUrl: 'project/crewmix/crewmixaf',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				prepareParam: (ident) => {
					const selectedProjectItem = this.projectEfbsheetstDataService.getSelection()[0];
					return { estCrewMixFk: selectedProjectItem?.Id ?? 0 };
				},
				usePost: true
			},

			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update',
				usePost: true
			},
			createInfo: {

				prepareParam: () => {
					const selection = this.projectEfbsheetstDataService.getSelectedEntity();
					return {
						estCrewMixFk: selection?.Id ?? 0
					};
				}
			},
			roleInfo: <IDataServiceChildRoleOptions<IPrjEfbsheetsMixAfEntity, IPrjEfbsheetsCrewMixEntity, IPrjCrewMixComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'PrjCrewMixAf',
				parent: parentService
			}
		};

		super(options);
	}

	/**
	 * @brief Indicates whether the current instance should be registered by method.
	 * This method always returns true, indicating that registration is permitted.
	 * @return `true` if registration by method is allowed; otherwise, `false`.
	 */
	public override registerByMethod(): boolean {
		return true;
	}



	/**
	 * @brief Retrieves the list of saved PrjEfbsheetsMixAf entities from the parent update.
	 * @param parentUpdate The complete parent update object containing crew mix AF data.
	 * @returns An array of IPrjEfbsheetsMixAfEntity objects to be saved.
	 */
	public override getSavedEntitiesFromUpdate(parentUpdate: IPrjCrewMixComplete): IPrjEfbsheetsMixAfEntity[] {
		return parentUpdate?.PrjCrewMixAfToSave ?? [];
	}

	/**
	 * @brief Registers modifications to the parent update object for crew mix AF entities.
	 * This method updates the `parentUpdate` object with the modified and deleted crew mix AF entities.
	 */
	public override async registerModificationsToParentUpdate(parentUpdate: IPrjCrewMixComplete, modified: IPrjEfbsheetsMixAfEntity[], deleted: IPrjEfbsheetsMixAfEntity[]) {
		console.log('ProjectEfbsheetsCrewMixAfDataService.registerModificationsToParentUpdate called', {
			modified: modified?.length || 0,
			deleted: deleted?.length || 0,
			parentUpdate
		});

		const selectedCreMix = this.projectEfbsheetsProjectDataService.getSelection()[0];
		if (modified && modified.length > 0) {
			console.log('Setting PrjCrewMixAfToSave:', modified);
			parentUpdate.PrjCrewMixAfToSave = modified;
		}
		if (deleted && deleted.length > 0) {
			console.log('Setting PrjCrewMixAfToDelete:', deleted);
			parentUpdate.PrjCrewMixAfToDelete = deleted;
			const basicsCommonToken = this.lazyInjector.inject(BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN);
			(await basicsCommonToken).calculateCrewmixesAndChilds(selectedCreMix, childType.AverageWage);
			(await basicsCommonToken).calculateCrewmixesAndChilds(selectedCreMix, childType.CrewmixAFSN);
			this.projectEfbsheetsProjectDataService.setModified(selectedCreMix);
			this.refresh();
		}

		console.log('Final parentUpdate after modifications:', parentUpdate);

		// After registering modifications, mark the parent crew mix as modified and trigger save
		if ((modified && modified.length > 0) || (deleted && deleted.length > 0)) {
			const selectedCrewMix = this.projectEfbsheetsProjectDataService.getSelectedEntity();
			if (selectedCrewMix) {
				console.log('Marking crew mix as modified:', selectedCrewMix);
				this.projectEfbsheetsProjectDataService.setModified(selectedCrewMix);

				// Trigger save at the project level
				console.log('Triggering project-level save...');
				setTimeout(async () => {
					try {
						if (this.projectMainDataService && typeof this.projectMainDataService.save === 'function') {
							await this.projectMainDataService.save();
							console.log('Project save completed successfully');
						} else {
							console.warn('Project main service not found or save method not available');
						}
					} catch (error) {
						console.error('Error during project save:', error);
					}
				}, 100); // Small delay to ensure modifications are fully registered
			}
		}
	}

	/**
	  * @brief Refreshes the data by reloading it from the parent selection.
	  *
	  * This method checks if there is a selected parent entity. If a parent is selected,
	 * it triggers a reload of the data by calling the `load` method with an object containing
	  * an `id` of `0`. This method is typically used to ensure that the latest data is
	  * loaded when the parent entity is available.
	  *
	  * @note If there is no parent selection, no action is taken.
	  */
	public refresh() {
		const parentSelection = this.projectEfbsheetsProjectDataService.getSelectedEntity();
		if (parentSelection) {
			this.load({ id: 0 });
		}
	}

	/**
	 * @brief Determines if the given entity is a child of the specified parent.
	 * This method compares the `EstCrewMixFk` property of the entity to the `Id` of the parent key
	 * to determine if the entity is associated with the parent.
	 */
	public override isParentFn(parentKey: IPrjEfbsheetsCrewMixEntity, entity: IPrjEfbsheetsMixAfEntity): boolean {
		return entity.EstCrewMixFk === parentKey.Id;
	}


	/**
	 * Handles the change event for a specific field in an entity.
	 * @param entity The entity being modified
	 * @param field The field name that changed
	 * @param value The new value for the field
	 * @returns This method does not return anything
	 */
	public async fieldChangeForMaster(entity: IPrjEfbsheetsMixAfEntity, field: string, value: number): Promise<void> {
		const selectedCrewMix = this.projectEfbsheetsProjectDataService.getSelectedEntity();
		if (!selectedCrewMix) {
			return;
		}

		let markupRate = entity.MarkupRate ?? 0;
		let percentHour = entity.PercentHour ?? 0;

		switch (field) {
			case 'MarkupRate':
				markupRate = value;
				break;
			case 'PercentHour':
				percentHour = value;
				break;
			case 'RateHour':
				entity.MarkupRate = 0;
				entity.PercentHour = 0;
				break;
			default:
				break;
		}

		selectedCrewMix.CrewMixAf =
			typeof selectedCrewMix.CrewMixAf === 'number' && isFinite(selectedCrewMix.CrewMixAf)
				? selectedCrewMix.CrewMixAf
				: 0;

		if (['PercentHour', 'MarkupRate', 'MdcWageGroupFk'].includes(field)) {
			if (markupRate && selectedCrewMix.CrewMixAf && percentHour) {
				entity.RateHour = selectedCrewMix.CrewMixAf * (markupRate / 100) * (percentHour / 100);
			}
		}

		if (['PercentHour', 'MarkupRate', 'RateHour', 'MdcWageGroupFk'].includes(field)) {
			const basicsCommonToken = this.lazyInjector.inject(BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN);
			const basicsCommon = await basicsCommonToken;
			basicsCommon.calculateCrewmixesAndChilds(selectedCrewMix, childType.CrewmixAF);
			basicsCommon.calculateCrewmixesAndChilds(selectedCrewMix, childType.CrewmixAFSN, true);
			this.projectEfbsheetsProjectDataService.setModified(selectedCrewMix);
		}
	}
}
