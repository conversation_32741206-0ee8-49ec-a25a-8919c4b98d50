/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import {
    BaseValidationService,
    IEntityRuntimeDataRegistry,
    IValidationFunctions,
    ValidationInfo
} from '@libs/platform/data-access';
import { ProjectEfbsheetsDataService } from '../project-efbsheets-data.service';
import { IPrjEfbsheetsCrewMixEntity } from '@libs/project/interfaces';


/**
 * @class ProjectCrewMixValidationService
 * @description Provides validation methods for Project CrewMix instances
 */
@Injectable({
    providedIn: 'root'
})
export class ProjectCrewMixValidationService extends BaseValidationService<IPrjEfbsheetsCrewMixEntity> {
    protected dataService = inject(ProjectEfbsheetsDataService);

    /**
     * Generates the validation functions for Project CrewMix
     * @returns An object containing the validation functions.
     */
    protected generateValidationFunctions(): IValidationFunctions<IPrjEfbsheetsCrewMixEntity> {
        return {
            Code: [this.validateIsRequired, this.validateIsLocalUnique]
        };
    }

    /**
     * Gets the entity runtime data registry for validation operations
     * @returns The data service that implements IEntityRuntimeDataRegistry
     */
    protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IPrjEfbsheetsCrewMixEntity> {
        return this.dataService;
    }

    /**
     * Gets the entity service for validation operations
     * @returns The data service that implements entity service APIs
     */
    protected override getEntityService = () => this.dataService;


}
