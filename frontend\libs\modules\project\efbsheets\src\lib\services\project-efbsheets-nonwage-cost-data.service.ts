/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable, InjectionToken} from '@angular/core';
import { BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN, childType } from '@libs/basics/interfaces';
import { DataServiceFlatNode, ServiceRole, IDataServiceOptions, IDataServiceChildRoleOptions, IDataServiceEndPointOptions } from '@libs/platform/data-access';
import { ProjectEfbsheetsDataService } from './project-efbsheets-data.service';
import {  PlatformLazyInjectorService } from '@libs/platform/common';
import { IPrjEfbsheetsCrewMixEntity, IPrjEfbsheetsNonwageCostsEntity } from '@libs/project/interfaces';
import { ProjectEfbsheetNonWageComplete } from '../model/project-efbsheets-crew-mix-nonwage-complet.class';
import { IPrjCrewMixComplete } from '../model/prj-crew-mix-complete.interface';

export const PROJECT_CREW_MIX_NONWAGE_COSTS_DATA_TOKEN = new InjectionToken<ProjectCrewMixNonwageCostsDataService>('projectCrewMixNonwageCostsDataToken');

@Injectable({
	providedIn: 'root'
})

/**
 * ProjectCrewMixNonwageCostsDataService
 */
export class ProjectCrewMixNonwageCostsDataService extends DataServiceFlatNode<IPrjEfbsheetsNonwageCostsEntity, ProjectEfbsheetNonWageComplete, IPrjEfbsheetsCrewMixEntity, IPrjCrewMixComplete> {
	private readonly parentService = inject(ProjectEfbsheetsDataService);
	private readonly lazyInjector = inject(PlatformLazyInjectorService);
	public constructor(private readonly projectEfbsheetsDataService: ProjectEfbsheetsDataService) {
		const options: IDataServiceOptions<IPrjEfbsheetsNonwageCostsEntity> = {
			apiUrl: 'project/crewmix/nonwagecosts',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: true,
				prepareParam: (ident) => {
					const selectedProjectItem = this.parentService.getSelection()[0];
					return { estCrewMixFk: selectedProjectItem?.Id ?? 0 };
				}
			},

			roleInfo: <IDataServiceChildRoleOptions<IPrjEfbsheetsNonwageCostsEntity, IPrjEfbsheetsCrewMixEntity, IPrjCrewMixComplete>>{
				role: ServiceRole.Node,
				itemName: 'PrjCrewMixNonwageCosts',
				parent: projectEfbsheetsDataService
			}
		};

		super(options);

	}

	/**
	 * @brief Wraps a modified non-wage cost entity into a complete update object.
	 * @param modified The modified non-wage cost entity or `null`.
	 * @returns A `ProjectEfbsheetNonWageComplete` containing the entity, if provided.
	 */

	public override createUpdateEntity(modified: IPrjEfbsheetsNonwageCostsEntity | null): ProjectEfbsheetNonWageComplete {
		const complete = new ProjectEfbsheetNonWageComplete();
		if (modified !== null) {
			complete.EstNonwageCosts = modified;
		}
		return complete;
	}

	/**
	* This method always returns `true`, indicating that registration by method is enable
	* @returns
	*/
	public override registerByMethod(): boolean {
		return true;
	}

	public override registerNodeModificationsToParentUpdate(parentUpdate: IPrjCrewMixComplete, modified: ProjectEfbsheetNonWageComplete[], deleted: IPrjEfbsheetsNonwageCostsEntity[]) {
		if (modified && modified.length > 0) {
			parentUpdate.PrjCrewMixNonwageCostsToSave = modified;
		}

		if (deleted && deleted.length > 0) {
			parentUpdate.PrjCrewMixNonwageCostsToDelete = deleted;
		}
	}

		public override getSavedEntitiesFromUpdate(complete: IPrjCrewMixComplete): IPrjEfbsheetsNonwageCostsEntity[] {
		return complete && complete.PrjCrewMixNonwageCostsToSave
			? complete.PrjCrewMixNonwageCostsToSave
				.map(item => item.EstNonwageCosts)
				.filter((entity): entity is IPrjEfbsheetsNonwageCostsEntity => entity !== undefined)
			: [];
	}

	/**
	 * Determines if the given entity is a child of the specified parent entity
	 * @param parentKey
	 * @param entity
	 * @returns
	 */
	public override isParentFn(parentKey: IPrjEfbsheetsNonwageCostsEntity, entity: IPrjEfbsheetsNonwageCostsEntity): boolean {
		return entity.EstCrewmixFk === parentKey.Id;
	}

	/**
	 *  Handles the change event for a specific field in an entity.
	 * @param field
	 * @param entity
	 * @returns This method does not return anything
	 */
	public async fieldChangeForMaster(entity: IPrjEfbsheetsNonwageCostsEntity, field: string, value: number): Promise<void> {
		const selectedCrewMix = this.parentService.getSelectedEntity();
		if (!selectedCrewMix) {
			return;
		}

		switch (field) {
			case 'Count':
				entity.Count = value;
				break;
			case 'RateDay':
				entity.RateDay = value;
				break;
			default:
				break;
		}

		if (['Count', 'RateDay', 'MdcWageGroupFk'].includes(field)) {
			const basicsCommonToken = this.lazyInjector.inject(BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN);
			const basicsCommon = await basicsCommonToken;
			basicsCommon.calculateCrewmixesAndChilds(selectedCrewMix, childType.NonwageCosts, true);
			this.parentService.setModified(selectedCrewMix);
		}
	}

}










