/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable, InjectionToken, Injector } from '@angular/core';
import { BasicsEfbsheetsCommonService } from '@libs/basics/efbsheets';

import { ServiceRole, IDataServiceOptions, IDataServiceChildRoleOptions, IDataServiceEndPointOptions, DataServiceFlatLeaf } from '@libs/platform/data-access';
import { IProjectEfbsheetsCrewMixAfsnComplete } from '../model/project-efbsheets-crew-mix-afsn-complete.interface';
import { BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN, childType } from '@libs/basics/interfaces';
import { ProjectEfbsheetsDataService } from './project-efbsheets-data.service';
import { IPrjEfbsheetsCrewMixEntity, IPrjEfbsheetsMixAfsnEntity } from '@libs/project/interfaces';
import { PlatformLazyInjectorService } from '@libs/platform/common';
import { IPrjCrewMixComplete } from '../model/prj-crew-mix-complete.interface';

export const PROJECT_EFBSHEETS_CREW_MIX_AFSN_DATA_TOKEN = new InjectionToken<ProjectEfbsheetsCrewMixAfsnDataService>('projectEfbsheetsCrewMixAfsnDataService');

@Injectable({
	providedIn: 'root'
})
export class ProjectEfbsheetsCrewMixAfsnDataService extends DataServiceFlatLeaf<IPrjEfbsheetsMixAfsnEntity,IPrjEfbsheetsCrewMixEntity, IPrjCrewMixComplete> {
	private readonly projectEfbsheetsProjectDataService = inject(ProjectEfbsheetsDataService);
	private readonly injector = inject(Injector);
	private readonly lazyInjector = inject(PlatformLazyInjectorService);
	public constructor(parentService: ProjectEfbsheetsDataService) {
		const options: IDataServiceOptions<IPrjEfbsheetsMixAfsnEntity> = {
			apiUrl: 'basics/efbsheets/crewmixafsn',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: true,
				prepareParam: (ident) => {
					const selectedProjectItem = this.projectEfbsheetsProjectDataService.getSelection()[0];
					return { estCrewMixFk: selectedProjectItem?.Id ?? 0 };
				}
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update'
			},
			createInfo: <IDataServiceEndPointOptions>{
				endPoint: 'create'
			},
			roleInfo: <IDataServiceChildRoleOptions<IPrjEfbsheetsMixAfsnEntity, IPrjEfbsheetsCrewMixEntity, IPrjCrewMixComplete>>{
				role: ServiceRole.Node,
				itemName: 'EstCrewMixAfsn',
				parent: parentService
			}
		};

		super(options);
	}
	
    /**
	 * @brief Indicates whether the current instance should be registered by method.
	 * This method always returns true, indicating that registration is permitted.
	 * @return `true` if registration by method is allowed; otherwise, `false`.
	 */
	public override registerByMethod(): boolean {
		return true;
	}

	/**
	 * @brief Registers node modifications to the parent update object.
	 *
	 * This method updates the parent object with modified and deleted crew mix details,
	 * calculates crew mixes and their child relationships, and assigns the selected crew mix
	 * to the parent update object if applicable.
	 *
	 * @param parentUpdate The parent update object of type `PrjCrewMixComplete` to be updated.
	 * @param modified An array of modified `IProjectEfbsheetsCrewMixAfsnComplete` objects.
	 * @param deleted An array of deleted `IProjectEfbsheetsCrewMixAfsnComplete` objects.
	 */
	public override registerModificationsToParentUpdate(parentUpdate: IPrjCrewMixComplete, modified: IProjectEfbsheetsCrewMixAfsnComplete[], deleted: IPrjEfbsheetsMixAfsnEntity[]) {
		if (modified && modified.length > 0) {
			parentUpdate.PrjCrewMixAfsnToSave = modified;
		}
		const selectedCreMix = this.projectEfbsheetsProjectDataService.getSelection()[0];
		if (deleted && deleted.length > 0) {
			parentUpdate.PrjCrewMixAfsnToDelete = deleted;
			const basicsEfbsheetsCommonService = this.injector.get(BasicsEfbsheetsCommonService);
			basicsEfbsheetsCommonService.calculateCrewmixesAndChilds(selectedCreMix, childType.CrewmixAFSN);
			parentUpdate.PrjCrewMix = selectedCreMix ? [selectedCreMix] : null;
		}
	}
 
	/**
	 * @brief Determines if the given entity is a child of the specified parent.
	 *
	 * This method checks whether the `EstCrewMixFk` property of the entity matches the `Id` of the parent key.
	 */
	public override isParentFn(parentKey: IPrjEfbsheetsCrewMixEntity, entity: IPrjEfbsheetsMixAfsnEntity): boolean {
		return entity.EstCrewMixFk === parentKey.Id;
	}

	/**
	 * Handles the change event for a specific field in an entity.
	 * @param entity The entity being modified
	 * @param field The field name that changed
	 * @param value The new value for the field
	 * @returns This method does not return anything
	 */
	public async fieldChangeForMaster(entity: IPrjEfbsheetsMixAfsnEntity, field: string, value: number): Promise<void> {
		const selectedCrewMix = this.projectEfbsheetsProjectDataService.getSelectedEntity();
		if (!selectedCrewMix) {
			return;
		}

		let markupRate = entity.MarkupRate ?? 0;
		let rateHour = entity.RateHour ?? 0;

		if (field === 'MarkupRate') {
			markupRate = value;
		}

		if (field === 'RateHour') {
			rateHour = value;
		}

		const { AverageStandardWage, CrewMixAf } = selectedCrewMix;
		if (AverageStandardWage == null || AverageStandardWage < 0 || CrewMixAf == null || CrewMixAf < 0) {
			return;
		}

		if (field === 'MarkupRate' || field === 'MdcWageGroupFk') {
			entity.RateHour = CrewMixAf * (markupRate / 100);
		}
		if (field === 'RateHour') {
			entity.MarkupRate = (rateHour / CrewMixAf) * 100;
		}

		this.projectEfbsheetsProjectDataService.setModified(selectedCrewMix);

		if (['MarkupRate', 'RateHour', 'MdcWageGroupFk'].includes(field)) {
			const basicsCommonToken = this.lazyInjector.inject(BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN);
			(await basicsCommonToken).calculateCrewmixesAndChilds(selectedCrewMix, childType.CrewmixAFSN);
		}
	}
}
