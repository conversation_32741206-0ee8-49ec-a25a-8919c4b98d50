/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable, InjectionToken } from '@angular/core';

import { ServiceRole, IDataServiceOptions, IDataServiceEndPointOptions, DataServiceFlatNode, IDataServiceChildRoleOptions } from '@libs/platform/data-access';
import { ProjectMainDataService } from '@libs/project/shared';
import { IPrjEfbsheetsCrewMixEntity, IProjectComplete, IProjectEntity } from '@libs/project/interfaces';
import { IPrjCrewMixComplete } from '../model/prj-crew-mix-complete.interface';


export const BASICS_EFBSHEETS_PROJECT_DATA_TOKEN = new InjectionToken<ProjectEfbsheetsDataService>('projectEfbsheetsProjectDataToken');

@Injectable({
	providedIn: 'root',
})
export class ProjectEfbsheetsDataService extends DataServiceFlatNode<IPrjEfbsheetsCrewMixEntity, IPrjCrewMixComplete, IProjectEntity, IProjectComplete> {
	public constructor() {
		const projectMainService = inject(ProjectMainDataService);
		const options: IDataServiceOptions<IPrjEfbsheetsCrewMixEntity> = {
			apiUrl: 'project/crewmix/crewmixes',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'listbyproject',
				usePost: true,
				prepareParam: (ident) => {
					const selectedProjectItem = projectMainService.getSelection()[0];
					return { ProjectFk: selectedProjectItem?.Id ?? 0 };
				},
			},
			createInfo: {
				prepareParam: () => {
					const selectedProjectItem = projectMainService.getSelection()[0];
					return { ProjectFk: selectedProjectItem?.Id ?? 0 };
				}
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update',
				usePost: true
			},
			roleInfo: <IDataServiceChildRoleOptions<IPrjEfbsheetsCrewMixEntity, IProjectEntity, IProjectComplete>>{
				role: ServiceRole.Node,
				itemName: 'EstCrewMix',
				parent: projectMainService,
			}
		};

		super(options);
	}

	/**
	 * @brief Indicates whether the current instance should be registered by method.
	 *
	 * This method always returns true, indicating that registration is permitted.
	 *
	 * @return `true` if registration by method is allowed; otherwise, `false`.
	 */
	public override registerByMethod(): boolean {
		return true;
	}

	/**
	 * @brief Generates a payload object for creating a new item.
	 *
	 * This method checks for a selected parent and uses its `Id` to populate the
	 * `ProjectFk` field in the payload. If no parent is selected, it returns an empty object.
	 *
	 * @return {object} The payload object for creation. Includes `ProjectFk` if a parent is selected, otherwise an empty object.
	 */
	protected override provideCreatePayload(): object {
		const parentSelection = this.getSelectedParent();
		if (parentSelection) {
			return {
				ProjectFk: parentSelection.Id
			};
		}

		return {};
	}

	/**
	 * This method constructs an `IBasicsEfbsheetsComplete` object based on the provided 
	 * `IBasicsEfbsheetsEntity`. It assigns the `Id` of the modified entity to the `MainItemId` property 
	   * and includes the entire modified entity as `EstCrewMix`. If the input is `null`, `EstCrewMix` is set to `null`.	
	 * @param {IBasicsEfbsheetsEntity | null} modified - The modified entity to be transformed into 
	   * an `IBasicsEfbsheetsComplete` object, or `null` if no entity is provided.
	   * @return {IBasicsEfbsheetsComplete} A complete entity object, containing the `MainItemId` and `EstCrewMix` properties.
	   */
	// public override createUpdateEntity(modified: IPrjEfbsheetsCrewMixEntity | null): IPrjCrewMixComplete {
	// 	const complete = new PrjCrewMixComplete();
	// 	if (modified !== null) {
	// 		complete.MainItemId = modified.Id;
	// 		complete.PrjCrewMix = modified;
	// 	}
	// 	return complete;
	// }

	public override createUpdateEntity(modified: IPrjEfbsheetsCrewMixEntity | null): IPrjCrewMixComplete {
		return  {
			Id : modified?.Id,
			PrjCrewMix: modified
		} as unknown as IPrjCrewMixComplete;
	}

	

	/**
	 * @brief Registers modifications to be saved and deletions to be removed for a given parent entity.
	 *
	 * @param complete The parent entity of type `IBasicsEfbsheetsComplete` to update.
	 * @param modified An array of modified `IBasicsEfbsheetsCrewMixAfsnComplete` entities to save.
	 * @param deleted An array of `IEstCrewMixAfsnEntity` entities to delete.
	 *
	 * @details If `modified` contains elements, assigns it to `complete.EstCrewMixAfsnToSave`.
	 *          If `deleted` contains elements, assigns it to `complete.EstCrewMixAfsnToDelete`.
	 */
	public override registerNodeModificationsToParentUpdate(parentUpdate: IProjectComplete, modified: IPrjCrewMixComplete[], deleted: IPrjEfbsheetsCrewMixEntity[]) {
		
		if (modified && modified.length > 0) {
			parentUpdate.PrjCrewMixToSave = modified;
		}
		if (deleted && deleted.length > 0) {
			parentUpdate.PrjCrewMixToSaveDelete = deleted;
		}
	}

	public override getSavedCompletesFromUpdate(parentUpdate: IProjectComplete): IPrjCrewMixComplete[] {
		return parentUpdate.PrjCrewMixToSave ?? [];
	}

	/**
	* @brief Retrieves the saved entities from the update process. 
	* This method extracts the `EstCrewMixToSave` property from the `complete` parameter 
	* if it exists; otherwise, it returns an empty array.
	 */
	public override getSavedEntitiesFromUpdate(complete: IProjectComplete): IPrjEfbsheetsCrewMixEntity[] {
		return complete && complete.PrjCrewMixToSave ? complete.PrjCrewMixToSave : [];
	}

	public override isParentFn(parentKey: IProjectEntity, entity: IPrjEfbsheetsCrewMixEntity): boolean {
		return entity.ProjectFk === parentKey.Id;
	}

	// service.copyMasterCrewMix = function copyMasterCrewMix(){                             // TODO basicsEfbsheetsCopyMasterCrewMixService  not ready
	// 	let selectedProjectItem = projectMainService.getSelected();
	// 	if(selectedProjectItem){
	// 		$injector.get('basicsEfbsheetsCopyMasterCrewMixService').showMasterCrewMixDialog(selectedProjectItem);
	// 	}
	// };
}
